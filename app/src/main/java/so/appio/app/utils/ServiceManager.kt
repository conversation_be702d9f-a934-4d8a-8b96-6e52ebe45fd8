package so.appio.app.utils

import android.content.Context
import android.util.Log
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext
import so.appio.app.data.database.DatabaseManager
import so.appio.app.data.entity.service.Service
import so.appio.app.data.entity.widget.Widget
import so.appio.app.data.entity.widget.WidgetResponse
import so.appio.app.network.APIService
import java.util.Date

/**
 * ServiceManager handles service-related operations including fetching service data
 * with widgets and storing them in the local database.
 */
class ServiceManager(
    private val context: Context,
    private val apiService: APIService = APIService
) {

    companion object {
        private const val TAG = "LOG:ServiceManager"
    }

    /**
     * Fetches service data with widgets from the API and stores it in the local database.
     * 
     * @param serviceId The ID of the service to fetch
     * @return Service entity if found and stored successfully, null if service not found
     */
    suspend fun fetchServiceWithWidgets(serviceId: String): Service? = withContext(Dispatchers.IO) {
        return@withContext try {
            Log.d(TAG, "Fetching service with widgets for serviceId: $serviceId")
            
            // Fetch service data from API
            val serviceResponse = apiService.fetchServiceWithWidgets(serviceId)
            
            // Convert ServiceResponse to Service entity
            val service = Service(
                id = serviceResponse.id,
                title = serviceResponse.title,
                description = serviceResponse.description,
                logoURL = serviceResponse.logoURL,
                bannerURL = serviceResponse.bannerURL,
                lastUpdate = Date(),
                lastSync = Date(),
                notificationsEnabled = true
            )
            
            // Store service in database
            val serviceRepository = DatabaseManager.getServiceRepository()
            serviceRepository.insertService(service)
            Log.d(TAG, "Service stored successfully: ${service.id}")
            
            // Process and store widgets if they exist
            serviceResponse.widgets?.let { widgetResponses ->
                val widgets = widgetResponses.map { widgetResponse ->
                    Widget(
                        id = widgetResponse.id,
                        serviceId = widgetResponse.serviceId,
                        name = widgetResponse.name,
                        config = widgetResponse.config,
                        updatedAt = Date()
                    )
                }

                if (widgets.isNotEmpty()) {
                    val widgetRepository = DatabaseManager.getWidgetRepository()
                    widgetRepository.insertWidgets(widgets)
                    Log.d(TAG, "Stored ${widgets.size} widgets for service: $serviceId")
                }
            }
            
            Log.d(TAG, "Successfully fetched and stored service with widgets: $serviceId")
            service
            
        } catch (e: Exception) {
            Log.e(TAG, "Failed to fetch service with widgets for serviceId: $serviceId", e)
            null
        }
    }
}
