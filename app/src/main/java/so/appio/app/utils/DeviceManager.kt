package so.appio.app.utils

import android.content.Context
import android.util.Log
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.flow.first
import kotlinx.coroutines.withContext
import so.appio.app.data.DeviceDataStore
import so.appio.app.data.entity.fingerprint.FingerprintRequest
import so.appio.app.data.entity.fingerprint.FingerprintResponse
import so.appio.app.network.APIService
import java.util.Locale
import java.util.TimeZone

class DeviceManager(
    private val context: Context,
    private val apiService: APIService = APIService
) {

    private val deviceDataStore = DeviceDataStore(context)
    private val deviceInfoManager = DeviceInfoManager(context)

    // Returns the DataStore instance representing the current device info
    suspend fun getCurrentDevice(): DeviceDataStore = deviceDataStore

    // Match fingerprint using detailed fingerprint request built from DeviceInfoManager
    suspend fun matchFingerprint(): FingerprintResponse? = withContext(Dispatchers.IO) {
        val fingerprintRequest = FingerprintRequest(
            userAgent =  "unknown", // We don't know what is the default browser
            screenResolution = deviceInfoManager.getScreenResolution(), // not reliable
            language = Locale.getDefault().toLanguageTag(),
            timeOffset = (TimeZone.getDefault().rawOffset / 60000), // minutes offset
        )
        apiService.fingerprintMatch(fingerprintRequest)
    }

    // Register and Link device using DeviceInfo from DeviceInfoManager, then update DataStore
    suspend fun registerDevice(serviceId: String, customerUserId: String) = withContext(Dispatchers.IO) {
        val deviceInfo = deviceInfoManager.collectDeviceInfo()
        val response = apiService.registerDevice(serviceId, customerUserId, deviceInfo)
        deviceDataStore.updateDeviceId(response.id)
        deviceDataStore.updateDeviceInfo(
            name = deviceInfo.name,
            osVersion = deviceInfo.osVersion,
            deviceIdentifier = deviceInfo.deviceIdentifier,
            model = deviceInfo.manufacturerModel,
        )
        deviceDataStore
    }

    // Link device to service
    private suspend fun linkDeviceService(deviceId: String, serviceId: String, customerUserId: String): Boolean =
        apiService.linkService(deviceId, serviceId, customerUserId)

    /**
     * Register or get device and link to service.
     * First checks if device ID exists in DataStore, if not registers a new device.
     * Then links the device to the service.
     *
     * @param serviceId The service ID to link to
     * @param customerUserId The customer user ID
     * @return DeviceDataStore instance
     */
    suspend fun registerOrGetDevice(serviceId: String, customerUserId: String): DeviceDataStore = withContext(Dispatchers.IO) {
        // Check if device ID already exists
        val existingDeviceId = deviceDataStore.deviceId.first()
        if (existingDeviceId != null) {
            Log.d("LOG:DeviceManager", "Device ID already exists: $existingDeviceId")
            linkDeviceService(existingDeviceId, serviceId, customerUserId)
            Log.d("LOG:DeviceManager", "Existing device linked to service and customer: $existingDeviceId -> $serviceId, $customerUserId")
            return@withContext deviceDataStore
        }

        Log.d("LOG:DeviceManager", "No device ID found, registering and link new device")
        val newDevice = registerDevice(serviceId, customerUserId)
        return@withContext newDevice
    }

    // Sync DataStore device info with current device info if out of sync
    suspend fun sync() {
        val currentInfo = deviceInfoManager.collectDeviceInfo()

        // Check if device identifier or OS version changed compared to DataStore
        val storedOsVersion = deviceDataStore.osVersion.first() ?: ""
        val storedDeviceIdentifier = deviceDataStore.deviceIdentifier.first() ?: ""
        val storedModel = deviceDataStore.model.first() ?: ""

        // TODO: unless Room checks update changes and triggers update event only if different
        if (storedDeviceIdentifier != currentInfo.deviceIdentifier
            || storedOsVersion != currentInfo.osVersion
            || storedModel != currentInfo.manufacturerModel
        ) {
            deviceDataStore.updateDeviceInfo(
                name = currentInfo.name,
                osVersion = currentInfo.osVersion,
                deviceIdentifier = currentInfo.deviceIdentifier,
                model = currentInfo.manufacturerModel,
            )

            // TODO: call API to sync. or let it be triggered by Room listener
            deviceDataStore.updateLastSync()
        }
    }
}
