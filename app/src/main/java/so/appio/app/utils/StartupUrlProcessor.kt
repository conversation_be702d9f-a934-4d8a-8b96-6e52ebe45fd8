package so.appio.app.utils

import android.content.Context
import android.util.Log
import kotlinx.coroutines.async
import kotlinx.coroutines.coroutineScope

private const val TAG = "LOG:StartupUrlProcessor"

data class URLParams(
    val serviceId: String,
    val showPreview: Boolean = false
)

/**
 * Centralized handler for all URL processing during app startup.
 * Determines whether to present ServiceScreen based on URL validation.
 *
 * Triggered by Intents, Install Referrer, and QR Scanner.
 */
object StartupUrlProcessor {

    /**
     * Processes a URL to determine the appropriate startup action.
     *
     * @param context Application context for initializing managers
     * @param data The URL string to process
     * @return URLParams|null
     */
    suspend fun processUrl(context: Context, data: String): URLParams? {
        Log.d(TAG, "Processing URL: $data")
        
        if (data.isBlank()) {
            Log.d(TAG, "Empty URL provided, returning NoAction")
            return null
        }
        
        // Validate and parse the URL using existing UrlValidator
        return when (val result = UrlValidator.validateAndParseUrl(data)) {
            is UrlValidationResult.Success -> {
                Log.d(TAG, "Valid URL found - serviceId: ${result.params.serviceId}, customerUserId: ${result.params.customerUserId}")

                try {
                    // Execute both operations simultaneously but wait for both to complete
                    coroutineScope {
                        val deviceManager = DeviceManager(context)
                        val serviceManager = ServiceManager(context)

                        // Start both operations simultaneously
                        val deviceDeferred = async {
                            Log.d(TAG, "Starting device registration/retrieval")
                            deviceManager.registerOrGetDevice(
                                serviceId = result.params.serviceId,
                                customerUserId = result.params.customerUserId ?: ""
                            )
                        }

                        val serviceDeferred = async {
                            Log.d(TAG, "Starting service data fetch")
                            serviceManager.fetchServiceWithWidgets(result.params.serviceId)
                        }

                        // Wait for both operations to complete
                        val device = deviceDeferred.await()
                        val service = serviceDeferred.await()

                        Log.d(TAG, "Device operation completed: ${device.deviceId}")
                        Log.d(TAG, "Service operation completed: ${service?.id}")

                        // Return null if service was not found
                        if (service == null) {
                            Log.e(TAG, "Service not found for serviceId: ${result.params.serviceId}")
                            return@coroutineScope null
                        }

                        URLParams(serviceId = result.params.serviceId, showPreview = false)
                    }
                } catch (e: Exception) {
                    Log.e(TAG, "Error processing URL: ${e.message}", e)
                    null
                }
            }
            is UrlValidationResult.Error -> {
                Log.e(TAG, "Invalid URL: ${result.message}")
                // Return immediately for invalid URLs (no delay)
                return null
            }
        }
    }
}
